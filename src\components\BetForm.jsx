import { useState, useEffect } from 'react';
import { betsAPI } from '../utils/api';

export default function BetForm({ students, selectedStudent, onSubmit }) {
  const [selectedTargets, setSelectedTargets] = useState([]);
  const [previousBets, setPreviousBets] = useState([]);
  const [hasExistingBets, setHasExistingBets] = useState(false);

  // 當選擇的學生改變時，檢查是否有之前的投資記錄
  useEffect(() => {
    if (selectedStudent) {
      checkPreviousBets();
    } else {
      setSelectedTargets([]);
      setPreviousBets([]);
      setHasExistingBets(false);
    }
  }, [selectedStudent?.seat_number]);

  const checkPreviousBets = async () => {
    try {
      const response = await betsAPI.getWeekly();
      const allBets = response.data;

      // 找出該學生的投資記錄
      const studentBets = allBets.filter(bet =>
        bet.bettor_seat_number === selectedStudent.seat_number
      );

      if (studentBets.length > 0) {
        setPreviousBets(studentBets);
        setHasExistingBets(true);
        setSelectedTargets([]); // 已投資過的學生不能再次投資
      } else {
        setPreviousBets([]);
        setHasExistingBets(false);
        setSelectedTargets([]);
      }
    } catch (error) {
      console.error('檢查投資記錄失敗:', error);
      setPreviousBets([]);
      setHasExistingBets(false);
      setSelectedTargets([]);
    }
  };

  // 過濾掉自己，不能對自己下注
  const availableTargets = students.filter(s => s.seat_number !== selectedStudent?.seat_number);

  const handleTargetSelect = (student) => {
    if (selectedTargets.find(s => s.seat_number === student.seat_number)) {
      // 取消選擇
      const newTargets = selectedTargets.filter(s => s.seat_number !== student.seat_number);
      setSelectedTargets(newTargets);
    } else if (selectedTargets.length < 3) {
      // 選擇新目標（最多3個）
      setSelectedTargets([...selectedTargets, student]);
    } else {
      alert('最多只能選擇3位同學下注！');
    }
  };

  const handleSubmit = () => {
    if (hasExistingBets) {
      return alert('本週已經投資過了，無法重複投資！');
    }

    if (selectedTargets.length !== 3) {
      return alert('請選擇剛好3位同學投資！');
    }

    const bets = selectedTargets.map(target => ({
      targetSeatNumber: target.seat_number,
      amount: 1 // 簡化為每位同學1點，總共3點
    }));

    onSubmit(bets);
  };

  if (!selectedStudent) {
    return (
      <div className="pastel-card">
        <div className="flex items-center gap-3 mb-4">
          <span className="text-3xl">💰</span>
          <h2 style={{fontFamily: 'Architects Daughter, cursive'}} className="text-2xl font-bold" style={{color: '#2d2d2d'}}>
            投資區
          </h2>
        </div>
        <div className="text-center py-8">
          <div className="text-6xl mb-4">🎯</div>
          <p style={{color: '#666666', fontFamily: 'Poppins, sans-serif', fontSize: '1.1rem'}}>
            請先選擇你的座號開始投資
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="pastel-card">
      <div className="flex items-center gap-3 mb-4">
        <span className="text-3xl">💰</span>
        <div>
          <h2 style={{fontFamily: 'Architects Daughter, cursive'}} className="text-2xl font-bold" style={{color: '#2d2d2d'}}>
            投資區
          </h2>
          <p style={{fontFamily: 'Poppins, sans-serif', color: '#666666'}}>
            {selectedStudent.name} (座號 {selectedStudent.seat_number})
          </p>
        </div>
      </div>

      {hasExistingBets ? (
        <div className="mb-6 p-4 rounded-2xl" style={{background: '#b8f2d0', border: '2px solid #90ee90'}}>
          <div className="flex items-center gap-3 mb-3">
            <span className="text-2xl">✅</span>
            <h3 style={{fontFamily: 'Architects Daughter, cursive', fontSize: '1.5rem', color: '#2d2d2d'}}>
              本週投資狀況
            </h3>
          </div>
          <p style={{fontFamily: 'Poppins, sans-serif', color: '#2d2d2d', fontSize: '1rem', marginBottom: '1rem'}}>
            你已經完成本週投資，投資了以下 {previousBets.length} 位同學：
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
            {previousBets.map((bet, index) => (
              <div key={index} className="p-2 bg-white dark:bg-gray-700 rounded border border-blue-200 dark:border-blue-600">
                <div className="font-medium text-gray-800 dark:text-white">
                  座號 {bet.target_seat_number}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-300">
                  {bet.target_name}
                </div>
                <div className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                  投資時間: {new Date(bet.created_at).toLocaleDateString('zh-TW')}
                </div>
              </div>
            ))}
          </div>
          <p className="text-xs text-blue-600 dark:text-blue-400 mt-3">
            💡 等待本週結算後，下週可以進行新的投資
          </p>
        </div>
      ) : (
        <p className="text-sm text-gray-500 mb-4">
          已選擇：{selectedTargets.length}/3 位同學 | 規則：選中3位同學，猜中1位就賺100點
        </p>
      )}

      {/* 學生選擇區 - 只在未投資時顯示 */}
      {!hasExistingBets && (
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3 text-gray-700 dark:text-gray-300">
            選擇要投資的同學 (剛好3位)
          </h3>
        <div className="grid grid-cols-7 gap-2 max-h-64 overflow-y-auto">
          {availableTargets.map(student => {
            const isSelected = selectedTargets.find(s => s.seat_number === student.seat_number);
            return (
              <button
                key={student.seat_number}
                onClick={() => handleTargetSelect(student)}
                className={`
                  p-2 rounded-lg border-2 transition-all duration-200 text-xs
                  ${isSelected
                    ? 'bg-green-600 text-white border-green-600 shadow-lg'
                    : 'bg-gray-50 dark:bg-gray-700 text-gray-700 dark:text-gray-300 border-gray-200 dark:border-gray-600 hover:bg-green-50 dark:hover:bg-gray-600 hover:border-green-300'
                  }
                `}
              >
                <div className="font-bold">{student.seat_number}</div>
                <div className="truncate">{student.name}</div>
              </button>
            );
          })}
          </div>
        </div>
      )}

      {/* 已選擇的學生 - 只在未投資且有選擇時顯示 */}
      {!hasExistingBets && selectedTargets.length > 0 && (
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3 text-gray-700 dark:text-gray-300">
            已選擇的同學
          </h3>
          <div className="space-y-2">
            {selectedTargets.map(student => (
              <div key={student.seat_number} className="flex items-center gap-3 p-3 bg-green-50 dark:bg-green-900/30 rounded-lg">
                <div className="flex-1">
                  <span className="font-medium text-gray-800 dark:text-white">
                    座號 {student.seat_number} - {student.name}
                  </span>
                </div>
                <span className="text-sm text-green-600 dark:text-green-400 font-medium">
                  ✓ 已選擇
                </span>
              </div>
            ))}
          </div>
        </div>
      )}

      {!hasExistingBets && (
        <button
          onClick={handleSubmit}
          className="w-full px-4 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors font-medium disabled:bg-gray-400"
          disabled={selectedTargets.length !== 3}
        >
          提交投資 ({selectedTargets.length}/3 位同學)
        </button>
      )}

      {hasExistingBets && (
        <div className="text-center">
          <button
            onClick={checkPreviousBets}
            className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
          >
            🔄 重新整理投資狀況
          </button>
        </div>
      )}
    </div>
  );
}
