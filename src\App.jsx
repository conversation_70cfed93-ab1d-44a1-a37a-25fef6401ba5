import { useState, useEffect } from 'react'
import BetForm from './components/BetForm'
import Leaderboard from './components/Leaderboard'
import StudentSelector from './components/StudentSelector'
import AdminPanel from './components/AdminPanel'
import MotivationPanel from './components/MotivationPanel'
import { FloatingStars } from './components/CuteIcons'
import { studentsAPI, scoresAPI, betsAPI, healthAPI } from './utils/api'

function App() {
  const [students, setStudents] = useState([])
  const [scores, setScores] = useState([])
  const [selectedStudent, setSelectedStudent] = useState(null)
  const [isDark, setIsDark] = useState(false)
  const [loading, setLoading] = useState(true)
  const [showAdmin, setShowAdmin] = useState(false)

  useEffect(() => {
    fetchStudents()
    fetchScores()
  }, [])

  const fetchStudents = async () => {
    try {
      const response = await studentsAPI.getAll()
      setStudents(response.data)
    } catch (error) {
      console.error('獲取學生資料失敗:', error)
    }
  }

  const fetchScores = async () => {
    try {
      const response = await scoresAPI.getCurrent()
      setScores(response.data)
      setLoading(false)
    } catch (error) {
      console.error('獲取分數資料失敗:', error)
      setLoading(false)
    }
  }

  const handleBetSubmit = async (bets) => {
    if (!selectedStudent) {
      alert('請先選擇你的座號')
      return
    }

    try {
      await betsAPI.submit(bets, selectedStudent.seat_number)
      alert(`投資成功！${selectedStudent.name} 已選擇 ${bets.length} 位同學`)
      fetchScores() // 重新獲取分數
    } catch (error) {
      console.error('投資失敗:', error)
      alert(error.response?.data?.error || '投資失敗，請稍後再試')
    }
  }

  const handleStudentSelect = (student) => {
    setSelectedStudent(student)
  }

  const handleStudentsUpdate = () => {
    fetchStudents()
  }

  const handleScoresUpdate = () => {
    fetchScores()
  }

  const toggleTheme = () => {
    setIsDark(!isDark)
    document.documentElement.classList.toggle('dark')
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-100 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-gray-600 dark:text-gray-300">載入中...</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen" style={{background: 'linear-gradient(135deg, #fff7fa 0%, #fef9fc 100%)'}}>
      <FloatingStars />
      <div className="container mx-auto px-4 py-8">
        <header className="flex justify-between items-center mb-12">
          <div className="flex items-center gap-4">
            <div className="text-6xl pastel-bounce">🌟</div>
            <div>
              <h1 style={{fontFamily: 'Architects Daughter, cursive'}} className="text-5xl font-bold mb-2"
                  style={{color: '#ffb6d5', textShadow: '2px 2px 4px rgba(255, 182, 213, 0.3)'}}>
                學生答題投資系統
              </h1>
              <p className="text-xl" style={{color: '#666666', fontFamily: 'Poppins, sans-serif'}}>
                答越多賺越多 💰 一起來投資同學吧！
              </p>
            </div>
          </div>
          <div className="flex gap-3">
            <button
              onClick={() => setShowAdmin(!showAdmin)}
              className="pastel-btn pastel-btn-secondary"
              style={{fontSize: '1.1rem', padding: '12px 24px'}}
            >
              {showAdmin ? '🎮 返回遊戲' : '⚙️ 管理員'}
            </button>
            <button
              onClick={toggleTheme}
              className="pastel-btn pastel-btn-accent"
              style={{fontSize: '1.1rem', padding: '12px 24px'}}
            >
              {isDark ? '🌞 亮色' : '🌙 暗色'}
            </button>
          </div>
        </header>

        {showAdmin ? (
          <AdminPanel
            students={students}
            scores={scores}
            onStudentsUpdate={handleStudentsUpdate}
            onScoresUpdate={handleScoresUpdate}
          />
        ) : (
          <div className="space-y-8">
            <StudentSelector
              students={students}
              selectedStudent={selectedStudent}
              onStudentSelect={handleStudentSelect}
            />

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div className="space-y-8">
                <BetForm
                  students={students}
                  selectedStudent={selectedStudent}
                  onSubmit={handleBetSubmit}
                />
                <MotivationPanel
                  selectedStudent={selectedStudent}
                  students={students}
                />
              </div>
              <div>
                <Leaderboard scores={scores} />
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default App
